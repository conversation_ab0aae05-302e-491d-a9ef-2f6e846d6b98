import React from 'react';

interface PrimaryButtonProps {
  /** Button content */
  children: React.ReactNode;
  /** Click handler */
  onClick?: () => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
  /** Icon to display before text */
  icon?: React.ReactNode;
}

/**
 * Primary button component following JobbLogg design system
 * 
 * @example
 * ```tsx
 * <PrimaryButton onClick={() => console.log('clicked')}>
 *   Opprett prosjekt
 * </PrimaryButton>
 * 
 * <PrimaryButton type="submit" loading={isLoading} icon={<PlusIcon />}>
 *   Lagre endringer
 * </PrimaryButton>
 * ```
 */
export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  loading = false,
  icon,
}) => {
  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled && !loading) {
      event.preventDefault();
      if (onClick) onClick();
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || loading}
      className={`
        btn-modern btn-primary-solid
        bg-jobblogg-primary text-white 
        hover:bg-jobblogg-primary-light 
        focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50
        disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-jobblogg-primary
        flex items-center justify-center gap-2
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      aria-disabled={disabled || loading}
    >
      {loading ? (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span>Laster...</span>
        </>
      ) : (
        <>
          {icon && <span className="flex-shrink-0">{icon}</span>}
          <span>{children}</span>
        </>
      )}
    </button>
  );
};

export default PrimaryButton;
