<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Toggle Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        .theme-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #1D4ED8;
            color: white;
        }
        button:hover {
            background: #1E40AF;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>JobbLogg Theme Toggle Test</h1>
        
        <div class="theme-info" id="themeInfo">
            Current theme: <span id="currentTheme">Loading...</span>
        </div>
        
        <button onclick="toggleTheme()">Toggle Theme</button>
        <button onclick="setLightTheme()">Set Light Theme</button>
        <button onclick="setDarkTheme()">Set Dark Theme</button>
        <button onclick="checkLocalStorage()">Check localStorage</button>
        
        <div class="theme-info" id="localStorageInfo">
            localStorage: <span id="localStorageValue">Loading...</span>
        </div>
        
        <div class="theme-info" id="consoleLog">
            Console output will appear here...
        </div>
    </div>

    <script>
        function updateThemeInfo() {
            const theme = document.documentElement.getAttribute('data-theme') || 'none';
            const localStorage = window.localStorage.getItem('jobblogg-theme') || 'none';
            
            document.getElementById('currentTheme').textContent = theme;
            document.getElementById('localStorageValue').textContent = localStorage;
            
            console.log('🎨 Current theme detected:', theme);
            console.log('💾 localStorage theme:', localStorage);
            
            // Update console log display
            const consoleDiv = document.getElementById('consoleLog');
            consoleDiv.innerHTML = `
                Console output:<br>
                🎨 Current theme detected: ${theme}<br>
                💾 localStorage theme: ${localStorage}
            `;
        }
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'jobblogg_light';
            const newTheme = currentTheme === 'jobblogg_light' ? 'jobblogg_dark' : 'jobblogg_light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('jobblogg-theme', newTheme);
            
            updateThemeInfo();
        }
        
        function setLightTheme() {
            document.documentElement.setAttribute('data-theme', 'jobblogg_light');
            localStorage.setItem('jobblogg-theme', 'jobblogg_light');
            updateThemeInfo();
        }
        
        function setDarkTheme() {
            document.documentElement.setAttribute('data-theme', 'jobblogg_dark');
            localStorage.setItem('jobblogg-theme', 'jobblogg_dark');
            updateThemeInfo();
        }
        
        function checkLocalStorage() {
            updateThemeInfo();
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check for saved theme preference
            const savedTheme = localStorage.getItem('jobblogg-theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
            } else {
                // Default to light theme
                document.documentElement.setAttribute('data-theme', 'jobblogg_light');
            }
            
            updateThemeInfo();
        });
    </script>
</body>
</html>
