<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clerk Theme Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        .theme-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #1D4ED8;
            color: white;
        }
        button:hover {
            background: #1E40AF;
        }
        .mock-clerk-card {
            background: var(--card-bg, #ffffff);
            color: var(--text-color, #1f2937);
            border: 1px solid var(--border-color, #e5e7eb);
            border-radius: 1rem;
            padding: 2rem;
            margin: 20px 0;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            transition: all 0.3s ease;
        }
        .mock-input {
            background: var(--input-bg, #ffffff);
            color: var(--text-color, #1f2937);
            border: 2px solid var(--border-color, #e5e7eb);
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            width: 100%;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Clerk Theme Configuration Test</h1>
        
        <div class="theme-info" id="themeInfo">
            Current theme: <span id="currentTheme">Loading...</span>
        </div>
        
        <button onclick="toggleTheme()">Toggle Theme</button>
        <button onclick="testClerkAppearance()">Test Clerk Appearance</button>
        
        <div class="theme-info" id="clerkAppearanceInfo">
            Clerk Appearance Configuration:
            <div id="appearanceOutput">Click "Test Clerk Appearance" to see configuration</div>
        </div>
        
        <div class="mock-clerk-card" id="mockCard">
            <h2>Mock Clerk Sign-In Card</h2>
            <p>This simulates how Clerk components would look with the current theme</p>
            <input type="email" class="mock-input" placeholder="Email address" />
            <input type="password" class="mock-input" placeholder="Password" />
            <button style="width: 100%; margin-top: 10px;">Sign In</button>
        </div>
    </div>

    <script>
        // Import the theme detection logic (simplified version)
        const getCurrentTheme = () => {
            if (typeof window === 'undefined') return 'jobblogg_light';
            const theme = document.documentElement.getAttribute('data-theme') || 'jobblogg_light';
            console.log('🎨 Current theme detected:', theme);
            return theme;
        };

        const themeColors = {
            light: {
                background: '#ffffff',
                backgroundSecondary: '#f9fafb',
                text: '#1f2937',
                textSecondary: '#6b7280',
                textMuted: '#9ca3af',
                border: '#e5e7eb',
                borderFocus: '#d1d5db',
                shimmer: '#f3f4f6',
                inputBackground: '#ffffff',
                cardBackground: '#ffffff',
            },
            dark: {
                background: '#1f2937',
                backgroundSecondary: '#374151',
                text: '#f9fafb',
                textSecondary: '#d1d5db',
                textMuted: '#9ca3af',
                border: '#4b5563',
                borderFocus: '#6b7280',
                shimmer: '#4b5563',
                inputBackground: '#374151',
                cardBackground: '#1f2937',
            }
        };

        function createClerkAppearance() {
            const currentTheme = getCurrentTheme();
            const isDark = currentTheme === 'jobblogg_dark';
            const colors = themeColors[isDark ? 'dark' : 'light'];

            return {
                isDark,
                currentTheme,
                colors,
                variables: {
                    colorPrimary: '#1D4ED8',
                    colorBackground: colors.background,
                    colorInputBackground: colors.inputBackground,
                    colorInputText: colors.text,
                    colorText: colors.text,
                    colorTextSecondary: colors.textSecondary,
                    colorBorder: colors.border,
                }
            };
        }

        function updateThemeInfo() {
            const theme = getCurrentTheme();
            document.getElementById('currentTheme').textContent = theme;
            
            // Update mock card styling
            const appearance = createClerkAppearance();
            const mockCard = document.getElementById('mockCard');
            
            mockCard.style.setProperty('--card-bg', appearance.colors.cardBackground);
            mockCard.style.setProperty('--text-color', appearance.colors.text);
            mockCard.style.setProperty('--border-color', appearance.colors.border);
            mockCard.style.setProperty('--input-bg', appearance.colors.inputBackground);
            
            if (appearance.isDark) {
                mockCard.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.5)';
            } else {
                mockCard.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
            }
        }
        
        function toggleTheme() {
            const currentTheme = getCurrentTheme();
            const newTheme = currentTheme === 'jobblogg_light' ? 'jobblogg_dark' : 'jobblogg_light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('jobblogg-theme', newTheme);
            
            updateThemeInfo();
        }
        
        function testClerkAppearance() {
            const appearance = createClerkAppearance();
            const output = document.getElementById('appearanceOutput');
            
            output.textContent = JSON.stringify(appearance, null, 2);
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check for saved theme preference
            const savedTheme = localStorage.getItem('jobblogg-theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
            } else {
                document.documentElement.setAttribute('data-theme', 'jobblogg_light');
            }
            
            updateThemeInfo();
        });
    </script>
</body>
</html>
