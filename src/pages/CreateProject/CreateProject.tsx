import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';

const CreateProject: React.FC = () => {
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const navigate = useNavigate();
  const createProject = useMutation(api.projects.create);
  const { user } = useUser();

  const validateForm = (formData: FormData) => {
    const newErrors: {[key: string]: string} = {};
    const projectName = formData.get('projectName') as string;

    if (!projectName || projectName.trim().length < 2) {
      newErrors.projectName = 'Prosjektnavn må være minst 2 tegn langt';
    }

    if (projectName && projectName.length > 100) {
      newErrors.projectName = 'Prosjektnavn kan ikke være lengre enn 100 tegn';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    // Store form reference before async operations
    const form = e.currentTarget;

    try {
      const formData = new FormData(form);

      // Validate form
      if (!validateForm(formData)) {
        setIsLoading(false);
        return;
      }

      const projectName = formData.get('projectName') as string;
      const description = formData.get('description') as string || '';

      if (!user?.id) {
        console.error('User not authenticated');
        setIsLoading(false);
        return;
      }

      await createProject({
        name: projectName.trim(),
        description: description.trim(),
        userId: user.id
      });

      // Reset form using stored reference
      form.reset();

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate('/'); // Navigate back to Dashboard after success
      }, 2000);

    } catch (error) {
      console.error('Error creating project:', error);
      setErrors({ general: 'Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white animate-fade-in">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Modern Header with Navigation */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
          <div className="flex items-center gap-4">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div className="animate-slide-up">
              <h1 className="text-4xl lg:text-5xl font-bold text-base-content bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Opprett nytt prosjekt
              </h1>
              <p className="text-base-content/60 mt-2 text-lg">
                Fyll ut informasjonen nedenfor for å starte et nytt prosjekt ✨
              </p>
            </div>
          </div>
        </div>

        {/* Modern Success Alert */}
        {showSuccess && (
          <div className="bg-success/10 border border-success/20 rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-success/20 rounded-full">
                <svg
                  className="w-6 h-6 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-success">Prosjekt opprettet! 🎉</h3>
                <p className="text-success/80 text-sm">Du blir snart omdirigert til oversikten...</p>
              </div>
            </div>
          </div>
        )}

        {/* General Error Alert */}
        {errors.general && (
          <div className="bg-error/10 border border-error/20 rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-error/20 rounded-full">
                <svg
                  className="w-6 h-6 text-error"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-error">Noe gikk galt</h3>
                <p className="text-error/80 text-sm">{errors.general}</p>
              </div>
            </div>
          </div>
        )}

        {/* Modern Create Project Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-base-100 rounded-xl shadow-lg p-8 animate-slide-up">
            <div className="mb-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-base-content mb-2">Prosjektdetaljer</h2>
              <p className="text-base-content/60">Gi prosjektet ditt et navn og en beskrivelse</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Project Name Field */}
              <div className="form-control">
                <label htmlFor="projectName" className="label">
                  <span className="label-text font-semibold text-base-content">Prosjektnavn *</span>
                </label>
                <input
                  type="text"
                  id="projectName"
                  name="projectName"
                  required
                  className={`input input-bordered input-modern w-full text-lg ${
                    errors.projectName ? 'input-error border-error' : 'focus:border-primary'
                  }`}
                  placeholder="F.eks. Kjøkkenrenovering, Terrasse bygging..."
                />
                {errors.projectName && (
                  <label className="label">
                    <span className="label-text-alt text-error flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {errors.projectName}
                    </span>
                  </label>
                )}
              </div>

              {/* Description Field */}
              <div className="form-control">
                <label htmlFor="description" className="label">
                  <span className="label-text font-semibold text-base-content">Beskrivelse</span>
                  <span className="label-text-alt text-base-content/60">(valgfritt)</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  className="textarea textarea-bordered input-modern w-full resize-none"
                  placeholder="Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer..."
                />
                <label className="label">
                  <span className="label-text-alt text-base-content/50">
                    💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere
                  </span>
                </label>
              </div>

              {/* Form Actions */}
              <div className="form-control mt-12">
                <button
                  type="submit"
                  className="btn btn-primary btn-lg btn-modern w-full shadow-lg hover:shadow-xl group"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Oppretter prosjekt...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Opprett prosjekt
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 max-w-2xl mx-auto">
          <div className="bg-base-200/50 rounded-xl p-6 text-center">
            <div className="flex items-center justify-center gap-6 text-sm text-base-content/60">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>* Obligatoriske felt</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span>Kan redigeres senere</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span>Sikker lagring</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateProject;
