@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Modern button styles */
  .btn-modern {
    @apply transition-all duration-200 transform hover:scale-105 focus:scale-105 focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded-xl font-medium px-6 py-3 shadow-sm hover:shadow-md;
  }

  /* Button variants */
  .btn-outline {
    @apply border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-primary focus:ring-opacity-50;
  }

  .btn-soft {
    @apply bg-jobblogg-primary-soft text-primary hover:bg-jobblogg-primary-light hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-primary focus:ring-opacity-50;
  }

  .btn-success-soft {
    @apply bg-jobblogg-accent-soft text-jobblogg-accent hover:bg-jobblogg-accent-light hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3;
  }

  .btn-warning-soft {
    @apply bg-jobblogg-warning-soft text-jobblogg-warning hover:bg-jobblogg-warning-light hover:text-jobblogg-text-strong transition-all duration-200 rounded-xl font-medium px-6 py-3;
  }

  .btn-error-soft {
    @apply bg-jobblogg-error-soft text-jobblogg-error hover:bg-jobblogg-error-light hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-xl hover:-translate-y-1 rounded-xl;
  }

  /* Input focus styles */
  .input-modern {
    @apply transition-all duration-200 focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-primary rounded-xl;
  }

  /* Alert components */
  .alert-success {
    @apply bg-jobblogg-accent-soft border border-jobblogg-accent-light text-jobblogg-text-strong rounded-xl p-4;
  }

  .alert-warning {
    @apply bg-jobblogg-warning-soft border border-jobblogg-warning-light text-jobblogg-text-strong rounded-xl p-4;
  }

  .alert-error {
    @apply bg-jobblogg-error-soft border border-jobblogg-error-light text-jobblogg-text-strong rounded-xl p-4;
  }

  .alert-info {
    @apply bg-jobblogg-primary-soft border border-jobblogg-primary-light text-jobblogg-text-strong rounded-xl p-4;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-base-300 rounded-xl;
  }

  /* Typography hierarchy */
  .text-heading-1 {
    @apply text-4xl lg:text-5xl font-bold text-jobblogg-text-strong;
  }

  .text-heading-2 {
    @apply text-3xl lg:text-4xl font-bold text-jobblogg-text-strong;
  }

  .text-heading-3 {
    @apply text-2xl lg:text-3xl font-semibold text-jobblogg-text-strong;
  }

  .text-body {
    @apply text-base text-jobblogg-text-medium;
  }

  .text-body-small {
    @apply text-sm text-jobblogg-text-medium;
  }

  .text-caption {
    @apply text-xs text-jobblogg-text-muted;
  }

  /* Card and container styles */
  .card-modern {
    @apply bg-base-100 rounded-xl shadow-sm border border-base-300 p-6;
  }

  .card-elevated {
    @apply bg-base-100 rounded-xl shadow-lg border border-base-300 p-6 hover:shadow-xl transition-shadow duration-200;
  }

  .container-section {
    @apply py-12 px-4;
  }

  .container-content {
    @apply max-w-7xl mx-auto;
  }

  /* Spacing utilities */
  .space-section {
    @apply mb-12;
  }

  .space-component {
    @apply mb-8;
  }

  .space-element {
    @apply mb-4;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-secondary;
  }

  .gradient-header {
    @apply bg-gradient-to-br from-jobblogg-primary to-jobblogg-accent;
  }

  .gradient-soft {
    @apply bg-gradient-to-br from-jobblogg-primary-soft to-jobblogg-accent-soft;
  }

  /* Smooth transitions for all elements */
  * {
    @apply transition-colors duration-200;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* Keyframes */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
