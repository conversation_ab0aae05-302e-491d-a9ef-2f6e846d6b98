/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'jobblogg': {
          'primary': '#1D4ED8',      // Modern blue for CTAs and active states
          'primary-light': '#3B82F6', // Lighter blue for hover states
          'primary-soft': '#DBEAFE',  // Soft blue for backgrounds
          'neutral': '#F8FAFC',      // Cooler light gray for card backgrounds
          'neutral-secondary': '#E5E7EB', // Secondary background with better contrast
          'accent': '#10B981',       // Green for success states
          'accent-light': '#34D399', // Lighter green for hover states
          'accent-soft': '#D1FAE5',  // Soft green for backgrounds
          'warning': '#FBBF24',      // Lighter amber for improved contrast
          'warning-light': '#FCD34D', // Lighter amber for hover states
          'warning-soft': '#FEF3C7', // Soft amber for backgrounds
          'error': '#DC2626',        // Deeper red for enhanced clarity
          'error-light': '#EF4444',  // Lighter red for hover states
          'error-soft': '#FEE2E2',   // Soft red for backgrounds
          'text-strong': '#1F2937',  // Dark gray for body text
          'text-medium': '#4B5563',  // Stronger gray for better legibility
          'text-muted': '#9CA3AF',   // Light gray for subtle text
        }
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('daisyui'),
  ],
  daisyui: {
    themes: [
      {
        jobblogg_light: {
          "primary": "#1D4ED8",
          "primary-focus": "#1E40AF",
          "primary-content": "#ffffff",
          "secondary": "#10B981",
          "secondary-focus": "#059669",
          "secondary-content": "#ffffff",
          "accent": "#FBBF24",
          "accent-focus": "#F59E0B",
          "accent-content": "#1F2937",
          "neutral": "#4B5563",
          "neutral-focus": "#6B7280",
          "neutral-content": "#ffffff",
          "base-100": "#ffffff",
          "base-200": "#F8FAFC",
          "base-300": "#E5E7EB",
          "base-content": "#1F2937",
          "info": "#3B82F6",
          "success": "#10B981",
          "warning": "#FBBF24",
          "error": "#DC2626",
        },

      },
      "light"
    ],
    base: true,
    styled: true,
    utils: true,
  },
}
