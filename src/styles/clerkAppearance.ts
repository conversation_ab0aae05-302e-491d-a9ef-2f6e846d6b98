// Clerk Appearance Configuration for JobbLogg
// Dynamic theme-aware configuration

// Theme detection utility
const getCurrentTheme = () => {
  if (typeof window === 'undefined') return 'light';
  const theme = document.documentElement.getAttribute('data-theme') || 'jobblogg_light';
  console.log('🎨 Current theme detected:', theme);
  return theme;
};

// Theme color definitions
const themeColors = {
  light: {
    background: '#ffffff',
    backgroundSecondary: '#f9fafb',
    text: '#1f2937',
    textSecondary: '#6b7280',
    textMuted: '#9ca3af',
    border: '#e5e7eb',
    borderFocus: '#d1d5db',
    shimmer: '#f3f4f6',
    inputBackground: '#ffffff',
    cardBackground: '#ffffff',
  },
  dark: {
    background: '#1f2937',
    backgroundSecondary: '#374151',
    text: '#f9fafb',
    textSecondary: '#d1d5db',
    textMuted: '#9ca3af',
    border: '#4b5563',
    borderFocus: '#6b7280',
    shimmer: '#4b5563',
    inputBackground: '#374151',
    cardBackground: '#1f2937',
  }
};

// Function to create theme-aware appearance
export const createClerkAppearance = () => {
  const currentTheme = getCurrentTheme();
  const isDark = currentTheme === 'jobblogg_dark';
  const colors = themeColors[isDark ? 'dark' : 'light'];

  return {
    variables: {
      // JobbLogg Brand Colors (consistent across themes)
      colorPrimary: '#1D4ED8',
      colorPrimaryFocus: '#1E40AF',
      colorSuccess: '#10B981',
      colorWarning: '#F59E0B',
      colorDanger: '#EF4444',

      // Typography
      fontFamily: 'Inter, system-ui, sans-serif',
      fontSize: '16px',
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },

      // Layout & Spacing
      borderRadius: '0.75rem',
      spacingUnit: '1rem',

      // Dynamic theme colors
      colorBackground: colors.background,
      colorInputBackground: colors.inputBackground,
      colorInputText: colors.text,
      colorText: colors.text,
      colorTextSecondary: colors.textSecondary,
      colorTextOnPrimaryBackground: '#ffffff',

      // Shadows and borders
      colorBorder: colors.border,
      colorShimmer: colors.shimmer,
    },
  
    elements: {
      // Main card container
      card: {
        backgroundColor: colors.cardBackground,
        borderRadius: '1rem',
        boxShadow: isDark
          ? '0 25px 50px -12px rgb(0 0 0 / 0.5)'
          : '0 25px 50px -12px rgb(0 0 0 / 0.25)',
        border: `1px solid ${colors.border}`,
        padding: '2rem',
        maxWidth: '28rem',
        width: '100%',
      },

      // Header styling
      headerTitle: {
        fontSize: '1.875rem',
        fontWeight: '700',
        color: colors.text,
        textAlign: 'center',
        marginBottom: '0.5rem',
      },

      headerSubtitle: {
        fontSize: '1rem',
        color: colors.textSecondary,
        textAlign: 'center',
        marginBottom: '2rem',
      },
    
    // Primary action button
    formButtonPrimary: {
      backgroundColor: '#1D4ED8',
      color: '#ffffff',
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '600',
      border: 'none',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.25)',
      width: '100%',
      height: '3rem',
      
      '&:hover': {
        backgroundColor: '#1E40AF',
        transform: 'scale(1.02)',
        boxShadow: '0 8px 25px 0 rgb(29 78 216 / 0.35)',
      },
      
      '&:focus': {
        outline: '2px solid #1D4ED8',
        outlineOffset: '2px',
        transform: 'scale(1.02)',
      },
      
      '&:active': {
        transform: 'scale(0.98)',
      },
    },
    
    // Secondary/outline buttons
    formButtonSecondary: {
      backgroundColor: 'transparent',
      color: colors.text,
      border: `2px solid ${colors.border}`,
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      width: '100%',
      height: '3rem',

      '&:hover': {
        borderColor: '#1D4ED8',
        color: '#1D4ED8',
        transform: 'scale(1.02)',
        boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.15)',
      },
    },
    
    // Social buttons (Apple, Google)
    socialButtonsBlockButton: {
      backgroundColor: colors.background,
      color: colors.text,
      border: `2px solid ${colors.border}`,
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      width: '100%',
      height: '3rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '0.75rem',
      marginBottom: '0.75rem',

      '&:hover': {
        borderColor: '#1D4ED8',
        transform: 'scale(1.02)',
        boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.15)',
      },

      '&:focus': {
        outline: '2px solid #1D4ED8',
        outlineOffset: '2px',
      },
    },
    
      // Input fields - CRITICAL for visibility
      formFieldInput: {
        backgroundColor: colors.inputBackground,
        color: colors.text,
        border: `2px solid ${colors.border}`,
        borderRadius: '0.75rem',
        padding: '0.75rem 1rem',
        fontSize: '1rem',
        width: '100%',
        height: '3rem',
        transition: 'all 0.2s ease',

        '&:focus': {
          borderColor: '#1D4ED8',
          outline: 'none',
          boxShadow: '0 0 0 3px rgb(29 78 216 / 0.1)',
          backgroundColor: colors.inputBackground,
        },

        '&::placeholder': {
          color: colors.textMuted,
        },
      },

      // Form field labels - CRITICAL for visibility
      formFieldLabel: {
        fontSize: '0.875rem',
        fontWeight: '600',
        color: colors.text,
        marginBottom: '0.5rem',
        display: 'block',
      },
    
      // Links - Maintain brand color for visibility
      formFieldAction: {
        color: '#1D4ED8',
        fontSize: '0.875rem',
        fontWeight: '500',
        textDecoration: 'none',
        transition: 'color 0.2s ease',

        '&:hover': {
          color: '#1E40AF',
          textDecoration: 'underline',
        },
      },

      // Footer links
      footerActionLink: {
        color: '#1D4ED8',
        fontSize: '0.875rem',
        fontWeight: '500',
        textDecoration: 'none',
        transition: 'color 0.2s ease',

        '&:hover': {
          color: '#1E40AF',
          textDecoration: 'underline',
        },
      },
    
    // Divider
    dividerLine: {
      backgroundColor: colors.border,
      height: '1px',
      margin: '1.5rem 0',
    },

    dividerText: {
      color: colors.textSecondary,
      fontSize: '0.875rem',
      fontWeight: '500',
    },
    
    // Loading spinner
    spinner: {
      color: '#1D4ED8',
      width: '1.5rem',
      height: '1.5rem',
    },
    
    // Error messages
    formFieldErrorText: {
      color: '#EF4444',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '0.5rem',
    },
    
    // Success messages
    formFieldSuccessText: {
      color: '#10B981',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '0.5rem',
    },
    
    // Alert/notification styling
    alert: {
      backgroundColor: colors.backgroundSecondary,
      border: `1px solid ${colors.border}`,
      borderRadius: '0.75rem',
      padding: '1rem',
      marginBottom: '1rem',
    },

      // Alert and error text - CRITICAL for visibility
      alertText: {
        color: colors.text,
        fontSize: '0.875rem',
        lineHeight: '1.5',
      },
    },
  };
};

// Create reactive appearance that updates with theme changes
export const getClerkAppearance = () => createClerkAppearance();

// Default export for static usage
export const clerkAppearance = createClerkAppearance();
